# Spotify API Credentials
# Get these from https://developer.spotify.com/dashboard/applications
SPOTIPY_CLIENT_ID=your_spotify_client_id_here
SPOTIPY_CLIENT_SECRET=your_spotify_client_secret_here
SPOTIPY_REDIRECT_URI=http://localhost:5000/callback

# Music Download Path
# IMPORTANT: Set this to your desired music folder path
# Examples:
# Windows: MUSIC_PATH=C:/Users/<USER>/Music
# Linux: MUSIC_PATH=/home/<USER>/Music  
# Mac: MUSIC_PATH=/Users/<USER>/Music
# Docker default: MUSIC_PATH=./music
MUSIC_PATH=./music

# Optional: Set your timezone
# Examples: America/New_York, Europe/London, Asia/Tokyo
TZ=UTC

# Optional: Flask settings (usually don't need to change these)
FLASK_ENV=production
FLASK_APP=app.py
