import spotipy
import dotenv
import os
import json
from spotipy.oauth2 import SpotifyOAuth

dotenv.load_dotenv()

scope = "user-library-read"
client_id = os.getenv("SPOTIFY_CLIENT_ID")
client_secret = os.getenv("SPOTIFY_CLIENT_SECRET")
redirect_uri = "http://localhost:8080/callback"

sp = spotipy.Spotify(
    auth_manager=SpotifyOAuth(
        client_id=client_id,
        client_secret=client_secret,
        redirect_uri=redirect_uri,
        scope=scope,
    )
)

def format_duration(ms):
    seconds = ms // 1000
    minutes = seconds // 60
    seconds = seconds % 60
    return f"{minutes}:{seconds:02d}"

def get_liked_songs():
    print("💖 Let's fetch your liked songs! Hold onto your heart! 💕 📚")
    results = sp.current_user_saved_tracks(limit=50)
    
    if not results:
        print("📭 No liked songs found. Empty mailbox! 📬")
        return []
    
    songs = []
    # Handle pagination
    while results:
        for item in results.get("items", []):
            track = item.get("track")
            if track:
                song = {
                    "name": track.get("name", ""),
                    "artist": track.get("artists", [{}])[0].get("name", ""),
                    "link": track.get("external_urls", {}).get("spotify", ""),
                    "image": track.get("album", {}).get("images", [{}])[0].get("url", ""),
                    "duration": format_duration(track.get("duration_ms", 0)),
                    "added_at": item.get("added_at", ""),
                }
                songs.append(song)
        
        # Get next page
        if results.get("next"):
            results = sp.next(results)
        else:
            break
    
    print(f"🎶 Found {len(songs)} liked songs! Time to rock! 🤘")
    return songs

def main():
    liked_songs = get_liked_songs()
    if not liked_songs:
        print("😞 No liked songs to save. Bummer! 😔")
        return
    with open('liked_songs.json', 'w') as f:
        json.dump(liked_songs, f, indent=4)
    print("💾 Saved your liked songs to liked_songs.json! Keep jamming! 🎸")

if __name__ == "__main__":
    main()