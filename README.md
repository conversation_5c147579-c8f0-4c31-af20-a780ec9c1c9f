# Artist Releases Downloader - Web Interface

A Flask-based web application that allows you to download your favorite artists' complete discographies or your liked songs from Spotify with a user-friendly interface.

## Features

- **Spotify OAuth Authentication**: Secure login with your Spotify account
- **Two Scanning Options**:
  - **Artist Scanning**: Download complete discographies from configured artists
  - **Liked Songs**: Download all your liked songs from Spotify
- **Real-time Progress Tracking**: Watch downloads happen with live updates
- **Sequential Processing**: Organized and reliable download process
- **Web-based Interface**: No more command-line interaction needed

## Prerequisites

- Python 3.7+
- Spotify Developer Account
- `spotdl` installed and configured
- Virtual environment (recommended)

## Setup

1. **Install Dependencies**:
   ```bash
   pip install flask flask-socketio spotipy python-dotenv
   ```

2. **Configure Spotify API**:
   - Create a Spotify app at https://developer.spotify.com/dashboard
   - Add `http://localhost:5000/callback` to your app's redirect URIs
   - Update your `.env` file with your credentials:
     ```
     SPOTIFY_CLIENT_ID=your_client_id_here
     SPOTIFY_CLIENT_SECRET=your_client_secret_here
     ```

3. **Prepare Artist Data** (for artist scanning):
   - Run `python get_all_artist_data.py` to generate `artist_songs.json`
   - This file contains the artist data that will be available for download

## Usage

1. **Start the Web Application**:
   ```bash
   python app.py
   ```

2. **Access the Interface**:
   - Open your browser and go to `http://localhost:5000`
   - Click "Connect with Spotify" to authenticate

3. **Choose Your Scanning Option**:
   - **Scan Specific Artists**: Downloads from your configured artist list
   - **Scan Liked Songs**: Downloads your personal Spotify liked songs

4. **Manage Artists** (for artist scanning):
   - Click "Add New Artist" to add artists via Spotify links
   - Copy artist URLs from Spotify (Share → Copy link to artist)
   - Remove artists by clicking the "×" button on artist cards

5. **Start Download**:
   - Select your preferred option
   - Click "Start Download"
   - Watch the real-time progress and logs

## File Structure

```
├── app.py                      # Main Flask application
├── templates/
│   ├── base.html              # Base template
│   ├── index.html             # Landing page
│   └── dashboard.html         # Main dashboard
├── static/
│   └── css/
│       └── style.css          # Custom styles
├── artist_songs.json          # Artist data (generated)
├── liked_songs.json           # Liked songs data (generated)
├── downloaded.json            # Download history for artists
├── downloaded_liked.json      # Download history for liked songs
├── queue_songs.json           # Current download queue for artists
├── queue_liked_songs.json     # Current download queue for liked songs
└── music/                     # Downloaded music files
    └── [Artist Name]/         # Artist-specific folders
```

## API Endpoints

- `GET /` - Landing page
- `GET /login` - Initiate Spotify OAuth
- `GET /callback` - OAuth callback handler
- `GET /dashboard` - Main dashboard (requires authentication)
- `GET /logout` - Logout and clear session
- `GET /api/artists` - Get configured artists data
- `GET /api/liked-songs-count` - Get count of user's liked songs
- `POST /api/add-artist` - Add a new artist by Spotify URL
- `POST /api/remove-artist` - Remove an artist from the list

## WebSocket Events

- `start_download` - Initiate download process
- `download_progress` - Real-time progress updates
- `download_log` - Live log messages
- `download_complete` - Download completion notification

## Original Command-Line Scripts

The original functionality is still available via command-line:

- `python get_all_artist_data.py` - Fetch artist data from Spotify
- `python get_user_fav_songs.py` - Fetch user's liked songs
- `python download_artists_songs.py` - Download artist songs
- `python download_liked_songs.py` - Download liked songs

## Technical Details

- **Framework**: Flask with Flask-SocketIO for real-time communication
- **Authentication**: Spotify OAuth 2.0 with PKCE
- **Download Tool**: spotdl (external dependency)
- **Real-time Updates**: WebSocket communication
- **File Management**: JSON-based tracking and queue management
- **Sequential Processing**: Downloads are processed one at a time to avoid rate limiting

## Troubleshooting

1. **Authentication Issues**:
   - Ensure your Spotify app redirect URI is set to `http://localhost:5000/callback`
   - Check that your `.env` file contains valid credentials

2. **Download Failures**:
   - Ensure `spotdl` is installed and working: `pip install spotdl`
   - Check that the Spotify links are valid and accessible

3. **Port Conflicts**:
   - If port 5000 is in use, modify the port in `app.py`
   - Update the redirect URI in your Spotify app settings accordingly

## License

This project is for educational and personal use. Respect Spotify's Terms of Service and copyright laws.
