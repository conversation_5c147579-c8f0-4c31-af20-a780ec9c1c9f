import spotipy
import dotenv
import os
import json
from spotipy.oauth2 import SpotifyOAuth

dotenv.load_dotenv()

scope = "user-library-read"
client_id = os.getenv("SPOTIFY_CLIENT_ID")
client_secret = os.getenv("SPOTIFY_CLIENT_SECRET")
redirect_uri = "http://localhost:8080/callback"

sp = spotipy.Spotify(
    auth_manager=SpotifyOAuth(
        client_id=client_id,
        client_secret=client_secret,
        redirect_uri=redirect_uri,
        scope=scope,
    )
)

artists = [
    "https://open.spotify.com/artist/4DzC4sTTWf2juzeeMiFB8O?si=6hzyb3uoTo-1g0yrFsewqQ",
    "https://open.spotify.com/artist/69zmLFTNPI5890lZXdOVjs?si=G7eCZaRJRLuEmXzXWL3dig",
    "https://open.spotify.com/artist/1PhE6rv0146ZTQosoPDjk8?si=EOpi1guKQKeis_7kjHEbIg",
    "https://open.spotify.com/artist/1ai0uMPwJsrTpNL6TFrboe?si=GFE4zZEVRA6IDS5MYggzCQ",
]


def format_duration(ms):
    seconds = ms // 1000
    minutes = seconds // 60
    seconds = seconds % 60
    return f"{minutes}:{seconds:02d}"


def get_artist_data(artist_id):
    print(f"🌸 Diving into artist {artist_id}'s world... Let's get their info! 🌺")
    try:
        artist = sp.artist(artist_id)
        if not artist:
            print(f"😞 No artist found for {artist_id}. Bummer! 😔")
            return None
        artist_info = {
            "name": artist.get("name", ""),
            "genres": artist.get("genres", []),
            "followers": artist.get("followers", {}).get("total", 0),
            "image": (
                artist.get("images", [{}])[0].get("url")
                if artist.get("images")
                else None
            ),
        }
        print(f"🎤 Found {artist_info['name']}! They have {artist_info['followers']} followers. Cool beans! 🫘")

        results = sp.artist_albums(artist_id, album_type="album,single")
        if not results:
            print(f"📭 No releases found for {artist_info['name']}. Empty mailbox! 📬")
            return None
        releases = results.get("items", [])
        print(f"🎶 Found {len(releases)} initial releases! Let's collect them all... 🎼")
        next_url = results.get("next")
        while next_url:
            results = sp.next(results)
            if not results:
                break
            releases.extend(results.get("items", []))
            next_url = results.get("next")
        print(f"📀 Total releases collected: {len(releases)}! Time to sort the goodies... 🍬")

        unique_releases = {}
        for release in releases:
            if release and release.get("name") not in unique_releases:
                unique_releases[release["name"]] = release

        albums = {}
        singles = {}

        for release in unique_releases.values():
            if not release:
                continue
            release_type = release.get("album_type")
            release_name = release.get("name")
            release_date = release.get("release_date")
            image = (
                release.get("images", [{}])[0].get("url")
                if release.get("images")
                else None
            )

            tracks = sp.album_tracks(release["id"])
            if not tracks:
                print(f"🎵 No tracks for {release_name}. Skipping this one... 😴")
                continue
            songs = []
            print(f"🎸 Processing tracks for '{release_name}'... Let's groove! 🕺")
            for track in tracks.get("items", []):
                if track:
                    song = {
                        "name": track.get("name", ""),
                        "link": track.get("external_urls", {}).get("spotify", ""),
                        "image": image,
                        "duration": format_duration(track.get("duration_ms", 0)),
                        "track_number": track.get("track_number", 0),
                    }
                    songs.append(song)
            next_tracks_url = tracks.get("next")
            while next_tracks_url:
                tracks = sp.next(tracks)
                if not tracks:
                    break
                for track in tracks.get("items", []):
                    if track:
                        song = {
                            "name": track.get("name", ""),
                            "link": track.get("external_urls", {}).get("spotify", ""),
                            "image": image,
                            "duration": format_duration(track.get("duration_ms", 0)),
                            "track_number": track.get("track_number", 0),
                        }
                        songs.append(song)
                next_tracks_url = tracks.get("next")
            print(f"✅ Added {len(songs)} songs from '{release_name}'! 🎉")

            # Sort songs by track number
            songs.sort(key=lambda x: x["track_number"])

            # Sort songs by track number
            songs.sort(
                key=lambda x: (
                    track.get("track_number", 0) if isinstance(track, dict) else 0
                )
            )

            release_data = {"release_date": release_date, "songs": songs}

            if release_type == "album":
                albums[release_name] = release_data
            elif release_type == "single":
                singles[release_name] = release_data

        # Sort albums and singles by release_date descending
        sorted_albums = dict(
            sorted(albums.items(), key=lambda x: x[1]["release_date"], reverse=True)
        )
        sorted_singles = dict(
            sorted(singles.items(), key=lambda x: x[1]["release_date"], reverse=True)
        )

        return {
            "artist_info": artist_info,
            "albums": sorted_albums,
            "singles": sorted_singles,
        }
    except Exception as e:
        print(f"😱 Oops! Error fetching data for artist {artist_id}: {e} 🐛")
        return None


def main():
    print("🎵 Starting the artist data adventure! Let's fetch some tunes! 🎶")
    all_data = {}
    for artist_url in artists:
        artist_id = artist_url.split("/")[-1].split("?")[0]
        print(f"🐱 Fetching data for artist {artist_id}... Hold onto your headphones! 🎧")
        data = get_artist_data(artist_id)
        if data:
            artist_name = data["artist_info"]["name"]
            print(f"✨ Got data for {artist_name}! Adding to the collection... 📚")
            all_data[artist_name] = data
        else:
            print(f"😿 Oops, couldn't fetch data for {artist_id}. Skipping... 🐾")

    print("💾 Saving all the artist goodies to artist_songs.json! 🌟")
    with open("artist_songs.json", "w") as f:
        json.dump(all_data, f, indent=4)
    print("🎉 All done! Your music data is ready to rock! 🚀")


if __name__ == "__main__":
    main()
