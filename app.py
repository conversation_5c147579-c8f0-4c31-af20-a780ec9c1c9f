from flask import Flask, render_template, request, redirect, url_for, session, jsonify, flash
from flask_socketio import Socket<PERSON>, emit
import os
import json
import spotipy
from spotipy.oauth2 import SpotifyOAuth
from dotenv import load_dotenv
import threading
import subprocess
import re
from datetime import datetime
import uuid

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SPOTIFY_CLIENT_SECRET', 'fallback-secret-key')
socketio = SocketIO(app, cors_allowed_origins="*")

# Spotify OAuth configuration
SPOTIFY_CLIENT_ID = os.getenv('SPOTIFY_CLIENT_ID')
SPOTIFY_CLIENT_SECRET = os.getenv('SPOTIFY_CLIENT_SECRET')
SPOTIFY_REDIRECT_URI = 'http://localhost:5000/callback'
SPOTIFY_SCOPE = 'user-library-read'

def get_spotify_oauth():
    return SpotifyOAuth(
        client_id=SPOTIFY_CLIENT_ID,
        client_secret=SPOTIFY_CLIENT_SECRET,
        redirect_uri=SPOTIFY_REDIRECT_URI,
        scope=SPOTIFY_SCOPE,
        cache_path='.cache',
        show_dialog=True
    )

def get_spotify_client():
    """Get authenticated Spotify client"""
    try:
        auth_manager = get_spotify_oauth()
        token_info = auth_manager.cache_handler.get_cached_token()

        if not token_info:
            return None

        if auth_manager.is_token_expired(token_info):
            token_info = auth_manager.refresh_access_token(token_info['refresh_token'])

        return spotipy.Spotify(auth=token_info['access_token'])
    except Exception as e:
        print(f"Error getting Spotify client: {e}")
        return None

@app.route('/')
def index():
    """Main landing page"""
    return render_template('index.html')

@app.route('/login')
def login():
    """Initiate Spotify OAuth login"""
    auth_manager = get_spotify_oauth()
    auth_url = auth_manager.get_authorize_url()
    return redirect(auth_url)

@app.route('/callback')
def callback():
    """Handle Spotify OAuth callback"""
    try:
        auth_manager = get_spotify_oauth()
        code = request.args.get('code')

        if not code:
            flash('Authorization failed. Please try again.', 'error')
            return redirect(url_for('index'))

        token_info = auth_manager.get_access_token(code)

        if not token_info:
            flash('Failed to get access token. Please try again.', 'error')
            return redirect(url_for('index'))

        # Test the token by getting user info
        sp = spotipy.Spotify(auth=token_info['access_token'])
        user_info = sp.current_user()

        session['user_id'] = user_info['id']
        session['user_name'] = user_info.get('display_name', user_info['id'])
        session['authenticated'] = True

        flash(f'Successfully logged in as {session["user_name"]}!', 'success')
        return redirect(url_for('dashboard'))

    except Exception as e:
        print(f"OAuth callback error: {e}")
        flash('Authentication failed. Please try again.', 'error')
        return redirect(url_for('index'))

@app.route('/logout')
def logout():
    """Logout and clear session"""
    if os.path.exists('.cache'):
        os.remove('.cache')
    session.clear()
    return redirect(url_for('index'))

@app.route('/dashboard')
def dashboard():
    """Main dashboard after authentication"""
    if not session.get('authenticated'):
        flash('Please log in to access the dashboard.', 'warning')
        return redirect(url_for('login'))

    sp = get_spotify_client()
    if not sp:
        flash('Your session has expired. Please log in again.', 'warning')
        return redirect(url_for('login'))

    return render_template('dashboard.html', user_name=session.get('user_name'))

@app.route('/api/artists')
def get_artists():
    """Get current artist list from artist_songs.json"""
    try:
        if os.path.exists('artist_songs.json'):
            with open('artist_songs.json', 'r') as f:
                data = json.load(f)
            
            artists = []
            for artist_name, artist_data in data.items():
                artist_info = artist_data.get('artist_info', {})
                artists.append({
                    'name': artist_name,
                    'followers': artist_info.get('followers', 0),
                    'genres': artist_info.get('genres', []),
                    'image': artist_info.get('image', ''),
                    'albums_count': len(artist_data.get('albums', {})),
                    'singles_count': len(artist_data.get('singles', {}))
                })
            
            return jsonify({'artists': artists})
        else:
            return jsonify({'artists': []})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/liked-songs-count')
def get_liked_songs_count():
    """Get count of user's liked songs"""
    sp = get_spotify_client()
    if not sp:
        return jsonify({'error': 'Not authenticated'}), 401

    try:
        # Get first page to get total count
        results = sp.current_user_saved_tracks(limit=1)
        total = results.get('total', 0)
        return jsonify({'count': total})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/add-artist', methods=['POST'])
def add_artist():
    """Add a new artist by Spotify URL"""
    if not session.get('authenticated'):
        return jsonify({'error': 'Not authenticated'}), 401

    sp = get_spotify_client()
    if not sp:
        return jsonify({'error': 'Spotify client not available'}), 401

    data = request.get_json()
    artist_url = data.get('artist_url', '').strip()

    if not artist_url:
        return jsonify({'error': 'Artist URL is required'}), 400

    try:
        # Extract artist ID from URL
        artist_id = extract_artist_id(artist_url)
        if not artist_id:
            return jsonify({'error': 'Invalid Spotify artist URL'}), 400

        # Check if artist already exists
        existing_artists = load_artist_data()
        for artist_name in existing_artists.keys():
            if existing_artists[artist_name].get('artist_info', {}).get('id') == artist_id:
                return jsonify({'error': f'Artist "{artist_name}" is already in your list'}), 400

        # Fetch artist data from Spotify
        artist_data = get_artist_data_by_id(sp, artist_id)
        if not artist_data:
            return jsonify({'error': 'Could not fetch artist data from Spotify'}), 400

        # Add to existing data
        artist_name = artist_data['artist_info']['name']
        existing_artists[artist_name] = artist_data

        # Save updated data
        save_artist_data(existing_artists)

        return jsonify({
            'success': True,
            'message': f'Successfully added "{artist_name}" to your artist list!',
            'artist': {
                'name': artist_name,
                'followers': artist_data['artist_info'].get('followers', 0),
                'genres': artist_data['artist_info'].get('genres', []),
                'image': artist_data['artist_info'].get('image', ''),
                'albums_count': len(artist_data.get('albums', {})),
                'singles_count': len(artist_data.get('singles', {}))
            }
        })

    except Exception as e:
        print(f"Error adding artist: {e}")
        return jsonify({'error': f'Failed to add artist: {str(e)}'}), 500

@app.route('/api/remove-artist', methods=['POST'])
def remove_artist():
    """Remove an artist from the list"""
    if not session.get('authenticated'):
        return jsonify({'error': 'Not authenticated'}), 401

    data = request.get_json()
    artist_name = data.get('artist_name', '').strip()

    if not artist_name:
        return jsonify({'error': 'Artist name is required'}), 400

    try:
        # Load existing data
        existing_artists = load_artist_data()

        if artist_name not in existing_artists:
            return jsonify({'error': f'Artist "{artist_name}" not found in your list'}), 404

        # Remove the artist
        del existing_artists[artist_name]

        # Save updated data
        save_artist_data(existing_artists)

        return jsonify({
            'success': True,
            'message': f'Successfully removed "{artist_name}" from your artist list!'
        })

    except Exception as e:
        print(f"Error removing artist: {e}")
        return jsonify({'error': f'Failed to remove artist: {str(e)}'}), 500

# WebSocket event handlers
@socketio.on('start_download')
def handle_start_download(data):
    """Handle download start request"""
    if not session.get('authenticated'):
        emit('error', {'message': 'Not authenticated'})
        return

    option = data.get('option')
    if option not in ['artists', 'liked']:
        emit('error', {'message': 'Invalid option'})
        return

    # Start download in background thread
    thread = threading.Thread(target=run_download, args=(option,))
    thread.daemon = True
    thread.start()

def run_download(option):
    """Run download process in background"""
    try:
        if option == 'artists':
            run_artists_download()
        elif option == 'liked':
            run_liked_download()
    except Exception as e:
        socketio.emit('download_log', {'message': f'Error: {str(e)}'})
        socketio.emit('download_complete', {'success': False, 'error': str(e)})

def normalize_filename(name):
    """Remove or replace invalid characters for filenames"""
    name = re.sub(r'[<>:"/\\|?*]', '', name)
    return name

def run_artists_download():
    """Run artist songs download with real-time updates"""
    socketio.emit('download_log', {'message': '🎵 Let\'s rock and roll! Starting the download party! 🎶'})

    # Load artist_songs.json
    socketio.emit('download_log', {'message': '📖 Loading artist_songs.json... Getting the playlist ready! 📚'})

    if not os.path.exists('artist_songs.json'):
        socketio.emit('download_log', {'message': '❌ artist_songs.json not found. Please run get_all_artist_data.py first.'})
        socketio.emit('download_complete', {'success': False, 'error': 'Artist data not found'})
        return

    with open('artist_songs.json', 'r') as f:
        data = json.load(f)
    socketio.emit('download_log', {'message': f'✨ Loaded data for {len(data)} artists! Time to groove! 🕺'})

    # Load or create downloaded.json
    downloaded_json_path = 'downloaded.json'
    if os.path.exists(downloaded_json_path):
        socketio.emit('download_log', {'message': '📋 Loading downloaded.json... Checking our download history! 📜'})
        with open(downloaded_json_path, 'r') as f:
            downloaded_songs = json.load(f)
        socketio.emit('download_log', {'message': f'📂 Found {len(downloaded_songs)} previously downloaded songs! 🎵'})
    else:
        downloaded_songs = []
        socketio.emit('download_log', {'message': '📋 No downloaded.json yet. Starting fresh! 🆕'})

    # Collect all songs
    socketio.emit('download_log', {'message': '🎸 Collecting all the songs from albums and singles... Let\'s make a mega playlist! 📀'})
    all_songs = []
    for artist, artist_data in data.items():
        for category in ['albums', 'singles']:
            for release_name, release_data in artist_data.get(category, {}).items():
                for song in release_data.get('songs', []):
                    all_songs.append({
                        'artist': artist,
                        'song_name': song['name'],
                        'link': song['link']
                    })
    socketio.emit('download_log', {'message': f'🎶 Collected {len(all_songs)} songs in total! Wow, that\'s a lot of tunes! 🎼'})

    # Continue with the rest of the download logic...
    process_downloads(all_songs, downloaded_songs, downloaded_json_path, data.keys())

def run_liked_download():
    """Run liked songs download with real-time updates"""
    socketio.emit('download_log', {'message': '💖 Let\'s download your liked songs! Hold onto your heart! 💕'})

    # First, fetch liked songs from Spotify
    sp = get_spotify_client()
    if not sp:
        socketio.emit('download_log', {'message': '❌ Not authenticated with Spotify'})
        socketio.emit('download_complete', {'success': False, 'error': 'Not authenticated'})
        return

    socketio.emit('download_log', {'message': '💖 Let\'s fetch your liked songs! Hold onto your heart! 💕 📚'})

    try:
        results = sp.current_user_saved_tracks(limit=50)
        if not results:
            socketio.emit('download_log', {'message': '📭 No liked songs found. Empty mailbox! 📬'})
            socketio.emit('download_complete', {'success': True})
            return

        songs = []
        # Handle pagination
        while results:
            for item in results.get("items", []):
                track = item.get("track")
                if track:
                    song = {
                        "name": track.get("name", ""),
                        "artist": track.get("artists", [{}])[0].get("name", ""),
                        "link": track.get("external_urls", {}).get("spotify", ""),
                        "image": track.get("album", {}).get("images", [{}])[0].get("url", ""),
                        "duration": format_duration(track.get("duration_ms", 0)),
                        "added_at": item.get("added_at", ""),
                    }
                    songs.append(song)

            # Get next page
            if results.get("next"):
                results = sp.next(results)
            else:
                break

        socketio.emit('download_log', {'message': f'🎶 Found {len(songs)} liked songs! Time to rock! 🤘'})

        # Save liked songs to file
        with open('liked_songs.json', 'w') as f:
            json.dump(songs, f, indent=4)
        socketio.emit('download_log', {'message': '💾 Saved your liked songs to liked_songs.json! Keep jamming! 🎸'})

    except Exception as e:
        socketio.emit('download_log', {'message': f'❌ Error fetching liked songs: {str(e)}'})
        socketio.emit('download_complete', {'success': False, 'error': str(e)})
        return

    # Load or create downloaded.json
    downloaded_json_path = 'downloaded_liked.json'
    if os.path.exists(downloaded_json_path):
        socketio.emit('download_log', {'message': '📋 Loading downloaded_liked.json... Checking our download history! 📜'})
        with open(downloaded_json_path, 'r') as f:
            downloaded_songs = json.load(f)
        socketio.emit('download_log', {'message': f'📂 Found {len(downloaded_songs)} previously downloaded liked songs! 🎵'})
    else:
        downloaded_songs = []
        socketio.emit('download_log', {'message': '📋 No downloaded_liked.json yet. Starting fresh! 🆕'})

    # Convert to format expected by process_downloads
    all_songs = []
    for song in songs:
        all_songs.append({
            'artist': song.get('artist', 'Unknown Artist'),
            'song_name': song.get('name', ''),
            'link': song.get('link', '')
        })

    socketio.emit('download_log', {'message': f'🎶 Collected {len(all_songs)} liked songs in total! Wow, that\'s a lot of tunes! 🎼'})

    # Get unique artists for directory creation
    unique_artists = set(song['artist'] for song in all_songs)

    process_downloads(all_songs, downloaded_songs, downloaded_json_path, unique_artists)

def format_duration(ms):
    """Format duration from milliseconds to MM:SS"""
    seconds = ms // 1000
    minutes = seconds // 60
    seconds = seconds % 60
    return f"{minutes}:{seconds:02d}"

def extract_artist_id(artist_url):
    """Extract artist ID from Spotify URL"""
    import re
    # Handle different Spotify URL formats
    patterns = [
        r'https://open\.spotify\.com/artist/([a-zA-Z0-9]+)',
        r'spotify:artist:([a-zA-Z0-9]+)'
    ]

    for pattern in patterns:
        match = re.search(pattern, artist_url)
        if match:
            return match.group(1)

    return None

def load_artist_data():
    """Load existing artist data from file"""
    if os.path.exists('artist_songs.json'):
        with open('artist_songs.json', 'r') as f:
            return json.load(f)
    return {}

def save_artist_data(data):
    """Save artist data to file"""
    with open('artist_songs.json', 'w') as f:
        json.dump(data, f, indent=4)

def get_artist_data_by_id(sp, artist_id):
    """Get artist data from Spotify API (adapted from get_all_artist_data.py)"""
    try:
        print(f"🌸 Diving into artist {artist_id}'s world... Let's get their info! 🌺")

        artist = sp.artist(artist_id)
        if not artist:
            print(f"😞 No artist found for {artist_id}. Bummer! 😔")
            return None

        artist_info = {
            "id": artist_id,  # Store the ID for duplicate checking
            "name": artist.get("name", ""),
            "genres": artist.get("genres", []),
            "followers": artist.get("followers", {}).get("total", 0),
            "image": (
                artist.get("images", [{}])[0].get("url")
                if artist.get("images")
                else None
            ),
        }
        print(f"🎤 Found {artist_info['name']}! They have {artist_info['followers']} followers. Cool beans! 🫘")

        results = sp.artist_albums(artist_id, album_type="album,single")
        if not results:
            print(f"📭 No releases found for {artist_info['name']}. Empty mailbox! 📬")
            return None

        releases = results.get("items", [])
        print(f"🎶 Found {len(releases)} initial releases! Let's collect them all... 🎼")

        # Handle pagination
        next_url = results.get("next")
        while next_url:
            results = sp.next(results)
            if not results:
                break
            releases.extend(results.get("items", []))
            next_url = results.get("next")
        print(f"📀 Total releases collected: {len(releases)}! Time to sort the goodies... 🍬")

        unique_releases = {}
        for release in releases:
            if release and release.get("name") not in unique_releases:
                unique_releases[release["name"]] = release

        albums = {}
        singles = {}

        for release in unique_releases.values():
            if not release:
                continue
            release_type = release.get("album_type")
            release_name = release.get("name")
            release_date = release.get("release_date")
            image = (
                release.get("images", [{}])[0].get("url")
                if release.get("images")
                else None
            )

            tracks = sp.album_tracks(release["id"])
            if not tracks:
                print(f"🎵 No tracks for {release_name}. Skipping this one... 😴")
                continue

            songs = []
            print(f"🎸 Processing tracks for '{release_name}'... Let's groove! 🕺")

            for track in tracks.get("items", []):
                if track:
                    song = {
                        "name": track.get("name", ""),
                        "link": track.get("external_urls", {}).get("spotify", ""),
                        "image": image,
                        "duration": format_duration(track.get("duration_ms", 0)),
                        "track_number": track.get("track_number", 0),
                    }
                    songs.append(song)

            # Handle track pagination
            next_tracks_url = tracks.get("next")
            while next_tracks_url:
                tracks = sp.next(tracks)
                if not tracks:
                    break
                for track in tracks.get("items", []):
                    if track:
                        song = {
                            "name": track.get("name", ""),
                            "link": track.get("external_urls", {}).get("spotify", ""),
                            "image": image,
                            "duration": format_duration(track.get("duration_ms", 0)),
                            "track_number": track.get("track_number", 0),
                        }
                        songs.append(song)
                next_tracks_url = tracks.get("next")

            print(f"✅ Added {len(songs)} songs from '{release_name}'! 🎉")

            # Sort songs by track number
            songs.sort(key=lambda x: x["track_number"])

            release_data = {"release_date": release_date, "songs": songs}

            if release_type == "album":
                albums[release_name] = release_data
            elif release_type == "single":
                singles[release_name] = release_data

        # Sort albums and singles by release_date descending
        sorted_albums = dict(
            sorted(albums.items(), key=lambda x: x[1]["release_date"], reverse=True)
        )
        sorted_singles = dict(
            sorted(singles.items(), key=lambda x: x[1]["release_date"], reverse=True)
        )

        return {
            "artist_info": artist_info,
            "albums": sorted_albums,
            "singles": sorted_singles,
        }

    except Exception as e:
        print(f"😱 Oops! Error fetching data for artist {artist_id}: {e} 🐛")
        return None

def process_downloads(all_songs, downloaded_songs, downloaded_json_path, artists):
    """Process downloads with real-time updates"""
    # List downloaded files, checking subfolders per artist
    music_dir = 'music'
    if not os.path.exists(music_dir):
        os.makedirs(music_dir)
        socketio.emit('download_log', {'message': '📁 Created music directory! Fresh start! 🆕'})

    socketio.emit('download_log', {'message': '🔍 Checking what songs are already downloaded... Let\'s see what\'s missing! 👀'})
    downloaded_files = {}
    for artist in artists:
        # Use normalized artist name for directory lookup
        normalized_artist = normalize_filename(artist)
        artist_dir = os.path.join(music_dir, normalized_artist)
        if os.path.exists(artist_dir):
            downloaded_files[artist] = [normalize_filename(f).lower() for f in os.listdir(artist_dir)]
            socketio.emit('download_log', {'message': f'📂 Found {len(downloaded_files[artist])} files for {artist}! 🎵'})
        else:
            downloaded_files[artist] = []
            socketio.emit('download_log', {'message': f'📂 No folder yet for {artist}. We\'ll create one! 🛠️'})

    # Find missing songs
    socketio.emit('download_log', {'message': '🕵️‍♀️ Hunting for missing songs... Which ones are we missing? 🔍'})
    missing_songs = []
    for song in all_songs:
        artist = song['artist']
        expected_filename = f"{song['artist']} - {song['song_name']}.mp3"
        normalized_expected = normalize_filename(expected_filename).lower()
        # Check if in folder
        in_folder = normalized_expected in downloaded_files.get(artist, [])
        # Check if in downloaded.json
        in_downloaded = any(ds['artist'] == song['artist'] and ds['song_name'] == song['song_name'] for ds in downloaded_songs)
        if not in_folder and not in_downloaded:
            missing_songs.append(song)
        elif in_downloaded and not in_folder:
            socketio.emit('download_log', {'message': f'⏭️ Skipping {song["artist"]} - {song["song_name"]}: Already downloaded before! 📋'})

    socketio.emit('download_log', {'message': f'📝 Found {len(missing_songs)} missing songs! Let\'s queue them up! 📋'})

    if len(missing_songs) == 0:
        socketio.emit('download_log', {'message': '🎉 All songs are already downloaded! Nothing to do! 🚀'})
        socketio.emit('download_complete', {'success': True})
        return

    # Save queue file
    queue_filename = 'queue_liked_songs.json' if 'liked' in downloaded_json_path else 'queue_songs.json'
    socketio.emit('download_log', {'message': f'💾 Saving the missing songs to {queue_filename}... Get ready to download! 🌟'})
    with open(queue_filename, 'w') as f:
        json.dump(missing_songs, f, indent=4)
    socketio.emit('download_log', {'message': '✅ Queue saved! Time to start downloading! 🚀'})

    # Download missing songs sequentially
    socketio.emit('download_log', {'message': '⬇️ Starting the download adventure! One song at a time... 🎧'})

    for i, song in enumerate(missing_songs):
        # Normalize artist name for directory creation
        normalized_artist = normalize_filename(song['artist'])
        artist_dir = os.path.join(music_dir, normalized_artist)
        if not os.path.exists(artist_dir):
            os.makedirs(artist_dir)
            socketio.emit('download_log', {'message': f'📁 Created folder for {song["artist"]}! 🆕'})

        current_song_info = f'{song["artist"]} - {song["song_name"]}'
        socketio.emit('download_log', {'message': f'🎵 Downloading {i+1}/{len(missing_songs)}: {current_song_info}... Hold tight! 🎶'})

        # Update progress
        socketio.emit('download_progress', {
            'completed': i,
            'total': len(missing_songs),
            'current_song': current_song_info
        })

        try:
            result = subprocess.run(['spotdl', song['link'], '--output', artist_dir],
                                  capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                socketio.emit('download_log', {'message': '✅ Downloaded successfully! 🎉'})
                # Add to downloaded_songs
                downloaded_songs.append({
                    'artist': song['artist'],
                    'song_name': song['song_name'],
                    'link': song['link'],
                    'downloaded_at': datetime.now().isoformat()
                })
                # Save downloaded.json
                with open(downloaded_json_path, 'w') as f:
                    json.dump(downloaded_songs, f, indent=4)
                socketio.emit('download_log', {'message': '📋 Added to downloaded.json! 📝'})
            else:
                socketio.emit('download_log', {'message': f'❌ Failed to download: {result.stderr} 😞'})
        except subprocess.TimeoutExpired:
            socketio.emit('download_log', {'message': f'⏰ Download timed out for {current_song_info} 😞'})
        except Exception as e:
            socketio.emit('download_log', {'message': f'😱 Error downloading {song["link"]}: {e} 🐛'})

    # Final progress update
    socketio.emit('download_progress', {
        'completed': len(missing_songs),
        'total': len(missing_songs),
        'current_song': 'Complete!'
    })

    socketio.emit('download_log', {'message': '🎊 All downloads complete! Your music library is now epic! 🚀'})
    socketio.emit('download_complete', {'success': True})

if __name__ == '__main__':
    # Create templates and static directories if they don't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
