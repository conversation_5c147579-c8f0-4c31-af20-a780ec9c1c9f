"""
Session Manager for Artist Releases Downloader
Handles persistent user sessions, Spotify tokens, and user settings
"""

import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
import spotipy
from spotipy.oauth2 import SpotifyOAuth


class SessionManager:
    """Manages persistent user sessions and settings"""
    
    def __init__(self, sessions_dir: str = "sessions"):
        self.sessions_dir = sessions_dir
        self.settings_dir = "user_settings"
        
        # Create directories if they don't exist
        os.makedirs(self.sessions_dir, exist_ok=True)
        os.makedirs(self.settings_dir, exist_ok=True)
    
    def get_session_file_path(self, user_id: str) -> str:
        """Get the file path for a user's session data"""
        return os.path.join(self.sessions_dir, f"{user_id}_session.json")
    
    def get_settings_file_path(self, user_id: str) -> str:
        """Get the file path for a user's settings"""
        return os.path.join(self.settings_dir, f"{user_id}_settings.json")
    
    def save_session(self, user_id: str, user_name: str, token_info: Dict[str, Any]) -> bool:
        """Save user session data to file"""
        try:
            session_data = {
                'user_id': user_id,
                'user_name': user_name,
                'token_info': token_info,
                'last_login': datetime.now().isoformat(),
                'created_at': datetime.now().isoformat()
            }
            
            session_file = self.get_session_file_path(user_id)
            
            # If session exists, preserve created_at
            if os.path.exists(session_file):
                try:
                    with open(session_file, 'r') as f:
                        existing_data = json.load(f)
                        session_data['created_at'] = existing_data.get('created_at', session_data['created_at'])
                except:
                    pass  # Use new created_at if file is corrupted
            
            with open(session_file, 'w') as f:
                json.dump(session_data, f, indent=2)
            
            print(f"✅ Session saved for user {user_name} ({user_id})")
            return True
            
        except Exception as e:
            print(f"❌ Error saving session for {user_id}: {e}")
            return False
    
    def load_session(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Load user session data from file"""
        try:
            session_file = self.get_session_file_path(user_id)
            
            if not os.path.exists(session_file):
                return None
            
            with open(session_file, 'r') as f:
                session_data = json.load(f)
            
            # Check if token is still valid or can be refreshed
            token_info = session_data.get('token_info')
            if token_info and self._is_token_valid_or_refreshable(token_info):
                return session_data
            else:
                print(f"⚠️ Token expired and not refreshable for user {user_id}")
                return None
                
        except Exception as e:
            print(f"❌ Error loading session for {user_id}: {e}")
            return None
    
    def _is_token_valid_or_refreshable(self, token_info: Dict[str, Any]) -> bool:
        """Check if token is valid or can be refreshed"""
        try:
            expires_at = token_info.get('expires_at', 0)
            current_time = int(time.time())
            
            # If token is still valid
            if expires_at > current_time:
                return True
            
            # If token is expired but has refresh token
            if token_info.get('refresh_token'):
                return True
            
            return False
            
        except Exception:
            return False
    
    def get_valid_spotify_client(self, user_id: str, spotify_oauth: SpotifyOAuth) -> Optional[spotipy.Spotify]:
        """Get a valid Spotify client for the user"""
        try:
            session_data = self.load_session(user_id)
            if not session_data:
                return None
            
            token_info = session_data['token_info']
            
            # Check if token needs refresh
            if spotify_oauth.is_token_expired(token_info):
                if token_info.get('refresh_token'):
                    print(f"🔄 Refreshing token for user {user_id}")
                    token_info = spotify_oauth.refresh_access_token(token_info['refresh_token'])
                    
                    # Save updated token
                    self.save_session(
                        session_data['user_id'],
                        session_data['user_name'],
                        token_info
                    )
                else:
                    print(f"❌ No refresh token available for user {user_id}")
                    return None
            
            return spotipy.Spotify(auth=token_info['access_token'])
            
        except Exception as e:
            print(f"❌ Error getting Spotify client for {user_id}: {e}")
            return None
    
    def get_all_active_users(self) -> list:
        """Get all users with valid sessions"""
        active_users = []
        
        try:
            if not os.path.exists(self.sessions_dir):
                return active_users
            
            for filename in os.listdir(self.sessions_dir):
                if filename.endswith('_session.json'):
                    user_id = filename.replace('_session.json', '')
                    session_data = self.load_session(user_id)
                    
                    if session_data:
                        active_users.append({
                            'user_id': user_id,
                            'user_name': session_data.get('user_name', user_id),
                            'last_login': session_data.get('last_login'),
                            'created_at': session_data.get('created_at')
                        })
            
        except Exception as e:
            print(f"❌ Error getting active users: {e}")
        
        return active_users
    
    def delete_session(self, user_id: str) -> bool:
        """Delete user session data"""
        try:
            session_file = self.get_session_file_path(user_id)
            
            if os.path.exists(session_file):
                os.remove(session_file)
                print(f"🗑️ Session deleted for user {user_id}")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Error deleting session for {user_id}: {e}")
            return False
    
    def save_user_settings(self, user_id: str, settings: Dict[str, Any]) -> bool:
        """Save user settings to file"""
        try:
            settings_file = self.get_settings_file_path(user_id)
            
            # Add metadata
            settings_data = {
                'user_id': user_id,
                'settings': settings,
                'updated_at': datetime.now().isoformat()
            }
            
            with open(settings_file, 'w') as f:
                json.dump(settings_data, f, indent=2)
            
            print(f"✅ Settings saved for user {user_id}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving settings for {user_id}: {e}")
            return False
    
    def load_user_settings(self, user_id: str) -> Dict[str, Any]:
        """Load user settings from file"""
        try:
            settings_file = self.get_settings_file_path(user_id)
            
            if not os.path.exists(settings_file):
                # Return default settings
                return self.get_default_settings()
            
            with open(settings_file, 'r') as f:
                settings_data = json.load(f)
            
            # Merge with defaults to ensure all settings exist
            default_settings = self.get_default_settings()
            user_settings = settings_data.get('settings', {})
            
            # Update defaults with user settings
            default_settings.update(user_settings)
            
            return default_settings
            
        except Exception as e:
            print(f"❌ Error loading settings for {user_id}: {e}")
            return self.get_default_settings()
    
    def get_default_settings(self) -> Dict[str, Any]:
        """Get default user settings"""
        return {
            'auto_check_enabled': False,
            'auto_check_interval_hours': 6,
            'auto_download_enabled': False,
            'check_on_startup': True,
            'download_artists': True,
            'download_liked': True,
            'notifications_enabled': True,
            'max_concurrent_downloads': 1,
            'created_at': datetime.now().isoformat()
        }
