import os
import re

def pad_number(num, digits=3):
    return f"{int(num):0{digits}d}"

def rename_files(root_dir):
    for series_dir in os.listdir(root_dir):
        series_path = os.path.join(root_dir, series_dir)
        if not os.path.isdir(series_path):
            continue
        series_name = series_dir
        # only do for folder D:\test\Manga\1-nen A-gumi no Monster
        # if series_name != '1-nen A-gumi no Monster':
        #     continue
        for filename in os.listdir(series_path):
            filepath = os.path.join(series_path, filename)
            if not os.path.isfile(filepath) or not (filename.endswith('.cbz') or filename.endswith('.zip')):
                continue
            # Parse Vol and Ch
            vol_match = re.search(r'(?:vol|v)\.?\s*(\d+)', filename.lower())
            ch_match = re.search(r'(?:ch|c)\.?\s*(\d+(?:\.\d+)?)', filename.lower())
            vol = vol_match.group(1) if vol_match else None
            ch = ch_match.group(1) if ch_match else None
            if not vol and not ch:
                # Try to find a standalone number as chapter
                num_match = re.search(r'(\d+)', filename)
                if num_match:
                    ch = num_match.group(1)
            ext = os.path.splitext(filename)[1]
            vol_padded = pad_number(vol) if vol else None
            ch_padded = None
            if ch:
                if '.' in ch:
                    parts = ch.split('.')
                    ch_padded = pad_number(parts[0]) + '.' + parts[1]
                else:
                    ch_padded = pad_number(ch)
            if vol and ch:
                new_name = f"{series_name} v{vol_padded} c{ch_padded}{ext}"
            elif vol:
                new_name = f"{series_name} v{vol_padded}{ext}"
            elif ch:
                new_name = f"{series_name} c{ch_padded}{ext}"
            else:
                print(f"Skipping {filename}: No Vol or Ch found")
                continue  # Skip if no vol/ch
            new_path = os.path.join(series_path, new_name)
            old_name = os.path.basename(filepath)
            print(f"Renaming:\nFrom: {old_name}\nTo:   {new_name}\n")
            if not os.path.exists(new_path):
                os.rename(filepath, new_path)

# Usage: rename_files('D:\\test\\Manga')

if __name__ == "__main__":
    root_directory = 'D:\\test\\Manga' 
    rename_files(root_directory)