import json
import os
import subprocess
import re
from datetime import datetime

def normalize_filename(name):
    # Remove or replace invalid characters for filenames
    name = re.sub(r'[<>:"/\\|?*]', '', name)
    return name

def safe_print(message):
    """Print message with Unicode fallback for Windows console"""
    try:
        print(message)
    except UnicodeEncodeError:
        # Remove emojis and special Unicode characters
        safe_message = re.sub(r'[^\x00-\x7F]+', '', message)
        print(safe_message)

def main():
    safe_print("🎵 Let's rock and roll! Starting the download party! 🎶")
    # Load artist_songs.json
    print("📖 Loading artist_songs.json... Getting the playlist ready! 📚")
    with open('artist_songs.json', 'r') as f:
        data = json.load(f)
    print(f"✨ Loaded data for {len(data)} artists! Time to groove! 🕺")
    
    # Load or create downloaded.json
    downloaded_json_path = 'downloaded.json'
    if os.path.exists(downloaded_json_path):
        safe_print("📋 Loading downloaded.json... Checking our download history! 📜")
        with open(downloaded_json_path, 'r') as f:
            downloaded_songs = json.load(f)
        safe_print(f"📂 Found {len(downloaded_songs)} previously downloaded artist songs! 🎵")
    else:
        downloaded_songs = []
        safe_print("📋 No downloaded.json yet. Starting fresh! 🆕")

    # Also load downloaded_liked.json to avoid duplicates
    all_downloaded_songs = list(downloaded_songs)
    if os.path.exists('downloaded_liked.json'):
        safe_print("📋 Also checking downloaded_liked.json to avoid duplicates... 🔍")
        with open('downloaded_liked.json', 'r') as f:
            liked_downloaded = json.load(f)
        # Add liked songs to our check list
        for liked_song in liked_downloaded:
            # Check if this song is already in our artist list
            already_exists = any(
                ds.get('artist') == liked_song.get('artist') and
                ds.get('song_name') == liked_song.get('song_name')
                for ds in all_downloaded_songs
            )
            if not already_exists:
                all_downloaded_songs.append(liked_song)
        safe_print(f"📊 Total songs to check against: {len(all_downloaded_songs)} (including liked songs)")
    else:
        safe_print("📝 No downloaded_liked.json found, only checking artist downloads")
        all_downloaded_songs = downloaded_songs
    
    # Collect all songs
    print("🎸 Collecting all the songs from albums and singles... Let's make a mega playlist! 📀")
    all_songs = []
    for artist, artist_data in data.items():
        for category in ['albums', 'singles']:
            for release_name, release_data in artist_data.get(category, {}).items():
                for song in release_data.get('songs', []):
                    all_songs.append({
                        'artist': artist,
                        'song_name': song['name'],
                        'link': song['link']
                    })
    print(f"🎶 Collected {len(all_songs)} songs in total! Wow, that's a lot of tunes! 🎼")
    
    # List downloaded files, checking subfolders per artist
    music_dir = 'music'
    if not os.path.exists(music_dir):
        os.makedirs(music_dir)
        print("📁 Created music directory! Fresh start! 🆕")
    
    print("🔍 Checking what songs are already downloaded... Let's see what's missing! 👀")
    downloaded_files = {}
    for artist in data.keys():
        artist_dir = os.path.join(music_dir, artist)
        if os.path.exists(artist_dir):
            downloaded_files[artist] = [normalize_filename(f).lower() for f in os.listdir(artist_dir)]
            print(f"📂 Found {len(downloaded_files[artist])} files for {artist}! 🎵")
        else:
            downloaded_files[artist] = []
            print(f"📂 No folder yet for {artist}. We'll create one! 🛠️")
    
    # Find missing songs
    print("🕵️‍♀️ Hunting for missing songs... Which ones are we missing? 🔍")
    missing_songs = []
    for song in all_songs:
        artist = song['artist']
        expected_filename = f"{song['artist']} - {song['song_name']}.mp3"
        normalized_expected = normalize_filename(expected_filename).lower()
        # Check if in folder
        in_folder = normalized_expected in downloaded_files.get(artist, [])
        # Check if in ANY downloaded.json (artists or liked)
        in_downloaded = any(
            ds.get('artist') == song['artist'] and ds.get('song_name') == song['song_name']
            for ds in all_downloaded_songs
        )
        if not in_folder and not in_downloaded:
            missing_songs.append(song)
        elif in_downloaded and not in_folder:
            safe_print(f"⏭️ Skipping {song['artist']} - {song['song_name']}: Already downloaded before! 📋")
    
    print(f"📝 Found {len(missing_songs)} missing songs! Let's queue them up! 📋")
    # Save queue_songs.json
    print("💾 Saving the missing songs to queue_songs.json... Get ready to download! 🌟")
    with open('queue_songs.json', 'w') as f:
        json.dump(missing_songs, f, indent=4)
    print("✅ Queue saved! Time to start downloading! 🚀")
    
    # Download missing songs sequentially
    print("⬇️ Starting the download adventure! One song at a time... 🎧")
    for i, song in enumerate(missing_songs):
        artist_dir = os.path.join(music_dir, song['artist'])
        if not os.path.exists(artist_dir):
            os.makedirs(artist_dir)
            print(f"📁 Created folder for {song['artist']}! 🆕")
        print(f"🎵 Downloading {i+1}/{len(missing_songs)}: {song['artist']} - {song['song_name']}... Hold tight! 🎶")
        try:
            result = subprocess.run(['spotdl', song['link'], '--output', artist_dir], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Downloaded successfully! 🎉")
                # Add to downloaded_songs
                downloaded_songs.append({
                    'artist': song['artist'],
                    'song_name': song['song_name'],
                    'link': song['link'],
                    'downloaded_at': datetime.now().isoformat()
                })
                # Save downloaded.json
                with open(downloaded_json_path, 'w') as f:
                    json.dump(downloaded_songs, f, indent=4)
                print("📋 Added to downloaded.json! 📝")
            else:
                print(f"❌ Failed to download: {result.stderr} 😞")
        except Exception as e:
            print(f"😱 Error downloading {song['link']}: {e} 🐛")
    print("🎊 All downloads complete! Your music library is now epic! 🚀")

if __name__ == "__main__":
    main()
