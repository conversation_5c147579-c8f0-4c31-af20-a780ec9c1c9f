{% extends "base.html" %}

{% block title %}Dashboard - Artist Releases Downloader{% endblock %}

{% block extra_head %}
<style>
.scan-option {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}
.scan-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
.scan-option.selected {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}
.artist-card {
    transition: transform 0.2s ease;
}
.artist-card:hover {
    transform: scale(1.02);
}
.progress-container {
    display: none;
}
.log-container {
    max-height: 400px;
    overflow-y: auto;
    background-color: #1a1a1a;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    padding: 1rem;
    border-radius: 0.375rem;
}
.activity-list, .jobs-list {
    max-height: 300px;
    overflow-y: auto;
}
.activity-item {
    border-left: 3px solid #007bff;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
}
.activity-item.success {
    border-left-color: #28a745;
}
.activity-item.error {
    border-left-color: #dc3545;
}
.activity-time {
    font-size: 0.8rem;
    color: #6c757d;
}
.job-item {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: #e9ecef;
    border-radius: 0.25rem;
    border-left: 3px solid #17a2b8;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            Welcome back, {{ user_name }}!
        </h1>
    </div>
</div>

<!-- Scanning Options -->
<div class="row mb-4">
    <div class="col-12">
        <h3 class="mb-3">Choose Your Scanning Option</h3>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card scan-option h-100" data-option="artists" onclick="selectOption('artists')">
            <div class="card-body text-center p-4">
                <div class="mb-3">
                    <i class="fas fa-users display-4 text-primary"></i>
                </div>
                <h4 class="card-title">Scan Specific Artists</h4>
                <p class="card-text">Download complete discographies from your configured artist list</p>
                <div class="mt-3">
                    <span class="badge bg-secondary" id="artists-count">Loading...</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card scan-option h-100" data-option="liked" onclick="selectOption('liked')">
            <div class="card-body text-center p-4">
                <div class="mb-3">
                    <i class="fas fa-heart display-4 text-danger"></i>
                </div>
                <h4 class="card-title">Scan Liked Songs</h4>
                <p class="card-text">Download all your liked songs from your Spotify account</p>
                <div class="mt-3">
                    <span class="badge bg-secondary" id="liked-count">Loading...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Artist Details (shown when artists option is selected) -->
<div id="artists-details" class="row mb-4" style="display: none;">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0">Your Configured Artists</h4>
            <button class="btn btn-outline-primary" onclick="showAddArtistModal()">
                <i class="fas fa-plus me-2"></i>Add New Artist
            </button>
        </div>
        <div id="artists-list" class="row">
            <!-- Artists will be loaded here -->
        </div>
    </div>
</div>

<!-- Download Controls -->
<div class="row mb-4">
    <div class="col-12 text-center">
        <button id="download-btn" class="btn btn-success btn-lg" disabled onclick="startDownload()">
            <i class="fas fa-download me-2"></i>
            Start Download
        </button>
    </div>
</div>

<!-- Progress Section -->
<div id="progress-section" class="progress-container">
    <div class="row mb-4">
        <div class="col-12">
            <h4>Download Progress</h4>
            <div class="progress mb-3" style="height: 25px;">
                <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                     role="progressbar" style="width: 0%">
                    <span id="progress-text">0%</span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title">Current Song</h5>
                            <p id="current-song" class="card-text">-</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title">Completed</h5>
                            <p id="completed-count" class="card-text">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title">Total</h5>
                            <p id="total-count" class="card-text">0</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Log Output -->
    <div class="row">
        <div class="col-12">
            <h4>Live Logs</h4>
            <div id="log-output" class="log-container">
                <div>Ready to start download...</div>
            </div>
        </div>
    </div>
</div>

<!-- Settings Section -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>Settings
                    <button class="btn btn-sm btn-outline-primary float-end" onclick="loadSettings()">
                        <i class="fas fa-sync me-1"></i>Refresh
                    </button>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-clock me-2"></i>Automatic Checking</h6>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoCheckEnabled">
                                <label class="form-check-label" for="autoCheckEnabled">
                                    Enable automatic checking for new releases
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="checkInterval" class="form-label">Check every:</label>
                            <select class="form-select" id="checkInterval">
                                <option value="1">1 hour</option>
                                <option value="3">3 hours</option>
                                <option value="6">6 hours</option>
                                <option value="12">12 hours</option>
                                <option value="24">24 hours</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="checkOnStartup">
                                <label class="form-check-label" for="checkOnStartup">
                                    Check for new releases on app startup
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-download me-2"></i>Automatic Downloads</h6>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoDownloadEnabled">
                                <label class="form-check-label" for="autoDownloadEnabled">
                                    Automatically download new releases
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="downloadArtists">
                                <label class="form-check-label" for="downloadArtists">
                                    Download new artist releases
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="downloadLiked">
                                <label class="form-check-label" for="downloadLiked">
                                    Download new liked songs
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="notificationsEnabled">
                                <label class="form-check-label" for="notificationsEnabled">
                                    Enable notifications
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" onclick="saveSettings()">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                            <button class="btn btn-outline-secondary" onclick="triggerManualCheck()">
                                <i class="fas fa-search me-2"></i>Check Now
                            </button>
                            <button class="btn btn-outline-info" onclick="loadActivity()">
                                <i class="fas fa-history me-2"></i>View Activity
                            </button>
                        </div>
                        <div id="settingsStatus" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Activity & Status Section -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>Recent Activity
                    <button class="btn btn-sm btn-outline-secondary float-end" onclick="loadActivity()">
                        <i class="fas fa-sync me-1"></i>Refresh
                    </button>
                </h6>
            </div>
            <div class="card-body">
                <div id="activityList" class="activity-list">
                    <div class="text-muted text-center">
                        <i class="fas fa-spinner fa-spin me-2"></i>Loading activity...
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clock me-2"></i>Scheduled Jobs
                    <button class="btn btn-sm btn-outline-secondary float-end" onclick="loadScheduledJobs()">
                        <i class="fas fa-sync me-1"></i>Refresh
                    </button>
                </h6>
            </div>
            <div class="card-body">
                <div id="scheduledJobsList" class="jobs-list">
                    <div class="text-muted text-center">
                        <i class="fas fa-spinner fa-spin me-2"></i>Loading jobs...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Artist Modal -->
<div class="modal fade" id="addArtistModal" tabindex="-1" aria-labelledby="addArtistModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addArtistModalLabel">
                    <i class="fas fa-plus me-2"></i>Add New Artist
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addArtistForm">
                    <div class="mb-3">
                        <label for="artistUrl" class="form-label">Spotify Artist URL</label>
                        <input type="url" class="form-control" id="artistUrl" placeholder="https://open.spotify.com/artist/..." required>
                        <div class="form-text">
                            Paste the Spotify link of the artist you want to add. You can get this by:
                            <br>1. Going to the artist's page on Spotify
                            <br>2. Clicking the "..." menu
                            <br>3. Selecting "Share" → "Copy link to artist"
                        </div>
                    </div>
                    <div id="addArtistError" class="alert alert-danger" style="display: none;"></div>
                    <div id="addArtistSuccess" class="alert alert-success" style="display: none;"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addArtist()" id="addArtistBtn">
                    <i class="fas fa-plus me-2"></i>Add Artist
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let selectedOption = null;
let socket = null;

// Initialize Socket.IO connection
function initSocket() {
    socket = io();
    
    socket.on('connect', function() {
        console.log('Connected to server');
    });
    
    socket.on('download_progress', function(data) {
        updateProgress(data);
    });
    
    socket.on('download_log', function(data) {
        addLogMessage(data.message);
    });
    
    socket.on('download_complete', function(data) {
        handleDownloadComplete(data);
    });
}

// Select scanning option
function selectOption(option) {
    selectedOption = option;
    
    // Update UI
    document.querySelectorAll('.scan-option').forEach(el => {
        el.classList.remove('selected');
    });
    document.querySelector(`[data-option="${option}"]`).classList.add('selected');
    
    // Show/hide artist details
    const artistsDetails = document.getElementById('artists-details');
    if (option === 'artists') {
        artistsDetails.style.display = 'block';
        loadArtists();
    } else {
        artistsDetails.style.display = 'none';
    }
    
    // Enable download button
    document.getElementById('download-btn').disabled = false;
}

// Load artists data
function loadArtists() {
    fetch('/api/artists')
        .then(response => response.json())
        .then(data => {
            const artistsList = document.getElementById('artists-list');
            artistsList.innerHTML = '';
            
            data.artists.forEach(artist => {
                const artistCard = createArtistCard(artist);
                artistsList.appendChild(artistCard);
            });
        })
        .catch(error => {
            console.error('Error loading artists:', error);
        });
}

// Create artist card element
function createArtistCard(artist) {
    const col = document.createElement('div');
    col.className = 'col-md-4 col-lg-3 mb-3';

    col.innerHTML = `
        <div class="card artist-card h-100">
            <div class="card-body text-center position-relative">
                <button class="btn btn-sm btn-outline-danger position-absolute top-0 end-0 m-2"
                        onclick="removeArtist('${artist.name.replace(/'/g, "\\'")}')"
                        title="Remove artist">
                    <i class="fas fa-times"></i>
                </button>
                ${artist.image ? `<img src="${artist.image}" class="rounded-circle mb-2" width="60" height="60">` : '<i class="fas fa-user-circle display-4 text-muted mb-2"></i>'}
                <h6 class="card-title">${artist.name}</h6>
                <small class="text-muted">
                    ${artist.followers.toLocaleString()} followers<br>
                    ${artist.albums_count} albums, ${artist.singles_count} singles
                </small>
            </div>
        </div>
    `;

    return col;
}

// Load counts on page load
function loadCounts() {
    // Load artists count
    fetch('/api/artists')
        .then(response => response.json())
        .then(data => {
            document.getElementById('artists-count').textContent = `${data.artists.length} artists configured`;
        })
        .catch(error => {
            document.getElementById('artists-count').textContent = 'Error loading';
        });
    
    // Load liked songs count
    fetch('/api/liked-songs-count')
        .then(response => response.json())
        .then(data => {
            document.getElementById('liked-count').textContent = `${data.count} liked songs`;
        })
        .catch(error => {
            document.getElementById('liked-count').textContent = 'Error loading';
        });
}

// Start download process
function startDownload() {
    if (!selectedOption) return;
    
    // Show progress section
    document.getElementById('progress-section').style.display = 'block';
    document.getElementById('download-btn').disabled = true;
    
    // Clear previous logs
    document.getElementById('log-output').innerHTML = '<div>Starting download...</div>';
    
    // Emit download start event
    socket.emit('start_download', {option: selectedOption});
}

// Update progress
function updateProgress(data) {
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const currentSong = document.getElementById('current-song');
    const completedCount = document.getElementById('completed-count');
    const totalCount = document.getElementById('total-count');
    
    const percentage = Math.round((data.completed / data.total) * 100);
    
    progressBar.style.width = percentage + '%';
    progressText.textContent = percentage + '%';
    currentSong.textContent = data.current_song || '-';
    completedCount.textContent = data.completed;
    totalCount.textContent = data.total;
}

// Add log message
function addLogMessage(message) {
    const logOutput = document.getElementById('log-output');
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
    logOutput.appendChild(logEntry);
    logOutput.scrollTop = logOutput.scrollHeight;
}

// Handle download completion
function handleDownloadComplete(data) {
    document.getElementById('download-btn').disabled = false;
    addLogMessage('🎊 All downloads complete! Your music library is now epic! 🚀');
}

// Show add artist modal
function showAddArtistModal() {
    document.getElementById('artistUrl').value = '';
    document.getElementById('addArtistError').style.display = 'none';
    document.getElementById('addArtistSuccess').style.display = 'none';
    const modal = new bootstrap.Modal(document.getElementById('addArtistModal'));
    modal.show();
}

// Add new artist
function addArtist() {
    const artistUrl = document.getElementById('artistUrl').value.trim();
    const errorDiv = document.getElementById('addArtistError');
    const successDiv = document.getElementById('addArtistSuccess');
    const addBtn = document.getElementById('addArtistBtn');

    // Hide previous messages
    errorDiv.style.display = 'none';
    successDiv.style.display = 'none';

    if (!artistUrl) {
        showError('Please enter a Spotify artist URL');
        return;
    }

    // Validate URL format
    const spotifyUrlPattern = /https:\/\/open\.spotify\.com\/artist\/[a-zA-Z0-9]+/;
    if (!spotifyUrlPattern.test(artistUrl)) {
        showError('Please enter a valid Spotify artist URL (e.g., https://open.spotify.com/artist/...)');
        return;
    }

    // Disable button and show loading
    addBtn.disabled = true;
    addBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding Artist...';

    fetch('/api/add-artist', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            artist_url: artistUrl
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            successDiv.textContent = data.message;
            successDiv.style.display = 'block';

            // Refresh artists list
            loadArtists();
            loadCounts();

            // Close modal after 2 seconds
            setTimeout(() => {
                bootstrap.Modal.getInstance(document.getElementById('addArtistModal')).hide();
            }, 2000);
        } else {
            showError(data.error || 'Failed to add artist');
        }
    })
    .catch(error => {
        console.error('Error adding artist:', error);
        showError('Network error. Please try again.');
    })
    .finally(() => {
        // Re-enable button
        addBtn.disabled = false;
        addBtn.innerHTML = '<i class="fas fa-plus me-2"></i>Add Artist';
    });

    function showError(message) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    }
}

// Remove artist
function removeArtist(artistName) {
    if (!confirm(`Are you sure you want to remove "${artistName}" from your artist list?`)) {
        return;
    }

    fetch('/api/remove-artist', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            artist_name: artistName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                ${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('main .container').insertBefore(alertDiv, document.querySelector('main .container').firstChild);

            // Refresh artists list
            loadArtists();
            loadCounts();

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        } else {
            alert('Error: ' + (data.error || 'Failed to remove artist'));
        }
    })
    .catch(error => {
        console.error('Error removing artist:', error);
        alert('Network error. Please try again.');
    });
}

// Settings functions
function loadSettings() {
    fetch('/api/settings')
        .then(response => response.json())
        .then(data => {
            if (data.settings) {
                const settings = data.settings;

                // Update form fields
                document.getElementById('autoCheckEnabled').checked = settings.auto_check_enabled || false;
                document.getElementById('checkInterval').value = settings.auto_check_interval_hours || 6;
                document.getElementById('checkOnStartup').checked = settings.check_on_startup !== false;
                document.getElementById('autoDownloadEnabled').checked = settings.auto_download_enabled || false;
                document.getElementById('downloadArtists').checked = settings.download_artists !== false;
                document.getElementById('downloadLiked').checked = settings.download_liked !== false;
                document.getElementById('notificationsEnabled').checked = settings.notifications_enabled !== false;

                showSettingsStatus('Settings loaded successfully', 'success');
            }
        })
        .catch(error => {
            console.error('Error loading settings:', error);
            showSettingsStatus('Error loading settings', 'error');
        });
}

function saveSettings() {
    const settings = {
        auto_check_enabled: document.getElementById('autoCheckEnabled').checked,
        auto_check_interval_hours: parseInt(document.getElementById('checkInterval').value),
        check_on_startup: document.getElementById('checkOnStartup').checked,
        auto_download_enabled: document.getElementById('autoDownloadEnabled').checked,
        download_artists: document.getElementById('downloadArtists').checked,
        download_liked: document.getElementById('downloadLiked').checked,
        notifications_enabled: document.getElementById('notificationsEnabled').checked
    };

    fetch('/api/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ settings: settings })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSettingsStatus('Settings saved successfully!', 'success');
        } else {
            showSettingsStatus('Error saving settings: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error saving settings:', error);
        showSettingsStatus('Network error while saving settings', 'error');
    });
}

function triggerManualCheck() {
    const button = document.querySelector('button[onclick="triggerManualCheck()"]');
    const originalText = button.innerHTML;

    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Checking...';

    fetch('/api/trigger-check', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSettingsStatus('Manual check triggered successfully!', 'success');
        } else {
            showSettingsStatus('Error triggering check: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error triggering check:', error);
        showSettingsStatus('Network error while triggering check', 'error');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

function showSettingsStatus(message, type) {
    const statusDiv = document.getElementById('settingsStatus');
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';

    statusDiv.innerHTML = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Auto-hide after 5 seconds
    setTimeout(() => {
        const alert = statusDiv.querySelector('.alert');
        if (alert) {
            alert.classList.remove('show');
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 150);
        }
    }, 5000);
}

// Activity and Jobs functions
function loadActivity() {
    fetch('/api/activity')
        .then(response => response.json())
        .then(data => {
            const activityList = document.getElementById('activityList');

            if (data.success && data.activity && data.activity.length > 0) {
                let html = '';
                data.activity.forEach(item => {
                    const timestamp = new Date(item.timestamp).toLocaleString();
                    const typeClass = item.type === 'auto_download' ?
                        (item.details && item.details.success ? 'success' : 'error') : '';

                    html += `
                        <div class="activity-item ${typeClass}">
                            <div class="activity-message">${item.message}</div>
                            <div class="activity-time">${timestamp}</div>
                        </div>
                    `;
                });
                activityList.innerHTML = html;
            } else {
                activityList.innerHTML = '<div class="text-muted text-center">No recent activity</div>';
            }
        })
        .catch(error => {
            console.error('Error loading activity:', error);
            document.getElementById('activityList').innerHTML =
                '<div class="text-danger text-center">Error loading activity</div>';
        });
}

function loadScheduledJobs() {
    fetch('/api/scheduled-jobs')
        .then(response => response.json())
        .then(data => {
            const jobsList = document.getElementById('scheduledJobsList');

            if (data.success) {
                let html = '';

                if (!data.scheduler_running) {
                    html += '<div class="alert alert-warning">Background scheduler is not running</div>';
                } else if (data.jobs && data.jobs.length > 0) {
                    data.jobs.forEach(job => {
                        const nextRun = job.next_run ? new Date(job.next_run).toLocaleString() : 'Not scheduled';
                        html += `
                            <div class="job-item">
                                <div class="job-name"><strong>${job.id}</strong></div>
                                <div class="job-next">Next run: ${nextRun}</div>
                                <div class="job-trigger">${job.trigger}</div>
                            </div>
                        `;
                    });
                } else {
                    html = '<div class="text-muted text-center">No scheduled jobs</div>';
                }

                jobsList.innerHTML = html;
            } else {
                jobsList.innerHTML = '<div class="text-danger text-center">Error loading jobs</div>';
            }
        })
        .catch(error => {
            console.error('Error loading scheduled jobs:', error);
            document.getElementById('scheduledJobsList').innerHTML =
                '<div class="text-danger text-center">Error loading jobs</div>';
        });
}

// Auto-refresh activity every 30 seconds
function startAutoRefresh() {
    setInterval(() => {
        loadActivity();
        loadScheduledJobs();
    }, 30000); // 30 seconds
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initSocket();
    loadCounts();
    loadSettings();
    loadActivity();
    loadScheduledJobs();
    startAutoRefresh();
});
</script>
{% endblock %}
