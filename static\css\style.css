/* Custom styles for Artist Releases Downloader */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 0.75rem 2rem;
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
}

.scan-option {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.scan-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.scan-option.selected {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

.artist-card {
    transition: transform 0.2s ease;
}

.artist-card:hover {
    transform: scale(1.02);
}

.progress {
    border-radius: 15px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 15px;
    background: linear-gradient(45deg, #28a745, #20c997);
}

.log-container {
    max-height: 400px;
    overflow-y: auto;
    background-color: #1a1a1a;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    padding: 1rem;
    border-radius: 0.375rem;
    border: 1px solid #333;
}

.log-container::-webkit-scrollbar {
    width: 8px;
}

.log-container::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: #777;
}

.feature-item {
    text-align: center;
    padding: 1rem;
}

.feature-item i {
    font-size: 2rem;
    display: block;
}

.feature-item p {
    margin: 0;
    font-size: 0.9rem;
}

.badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
}

.display-1 {
    font-size: 4rem;
}

.display-4 {
    font-size: 2.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .display-1 {
        font-size: 3rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .btn-lg {
        padding: 0.75rem 2rem;
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
}

/* Animation for progress bar */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.progress-bar-animated {
    animation: pulse 2s infinite;
}

/* Custom alert styles */
.alert {
    border-radius: 15px;
    border: none;
}

/* Footer styles */
footer {
    margin-top: auto;
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom button variants */
.btn-spotify {
    background-color: #1db954;
    border-color: #1db954;
    color: white;
}

.btn-spotify:hover {
    background-color: #1ed760;
    border-color: #1ed760;
    color: white;
}

/* Status indicators */
.status-success {
    color: #28a745;
}

.status-error {
    color: #dc3545;
}

.status-warning {
    color: #ffc107;
}

.status-info {
    color: #17a2b8;
}
