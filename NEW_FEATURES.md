# New Features: Session Management & Automatic Checking

## 🎉 What's New

### 1. **Persistent Session Management**
- **Session Persistence**: Your Spotify login now persists across app restarts
- **Automatic Token Refresh**: Tokens are automatically refreshed when needed
- **Multi-User Support**: Multiple users can have saved sessions
- **Secure Storage**: Sessions are stored securely in the `sessions/` directory

### 2. **Automatic Release Checking**
- **Periodic Checks**: Automatically check for new releases every 1-24 hours
- **Startup Checks**: Option to check for new releases when the app starts
- **Background Processing**: All checks run in the background without interrupting your workflow
- **Smart Detection**: Detects new artist releases and liked songs changes

### 3. **Automatic Downloads**
- **Auto-Download**: Automatically download new releases when found
- **Selective Downloads**: Choose to auto-download artists, liked songs, or both
- **Queue Management**: New releases are queued and downloaded sequentially
- **Download History**: All downloads are tracked and logged

### 4. **Enhanced Settings Interface**
- **User-Friendly Settings**: Easy-to-use settings panel in the dashboard
- **Real-Time Updates**: Settings take effect immediately
- **Manual Triggers**: Manually trigger checks with the "Check Now" button
- **Status Notifications**: Get feedback on all actions

## 🚀 How to Use

### Initial Setup
1. **Login**: Connect with your Spotify account as usual
2. **Session Saved**: Your session is automatically saved for future use
3. **Configure Settings**: Visit the Settings section in the dashboard

### Settings Configuration

#### Automatic Checking
- **Enable/Disable**: Toggle automatic checking on/off
- **Check Interval**: Choose how often to check (1, 3, 6, 12, or 24 hours)
- **Startup Check**: Enable checking when the app starts

#### Automatic Downloads
- **Auto-Download**: Enable automatic downloading of new releases
- **Artist Releases**: Auto-download new releases from your configured artists
- **Liked Songs**: Auto-download newly liked songs
- **Notifications**: Enable/disable status notifications

### Manual Operations
- **Check Now**: Manually trigger a check for new releases
- **Save Settings**: Apply your configuration changes
- **Refresh**: Reload current settings

## 📁 File Structure

### New Directories
```
sessions/           # User session data
├── user1_session.json
└── user2_session.json

user_settings/      # User preferences
├── user1_settings.json
└── user2_settings.json

logs/              # Activity logs
├── user1_new_releases.json
└── background_tasks.log
```

### New Files
- `session_manager.py` - Handles persistent sessions and user settings
- `background_scheduler.py` - Manages periodic tasks and automatic checking
- `test_session_manager.py` - Test script for session functionality

## 🔧 Technical Details

### Dependencies Added
- `apscheduler==3.10.4` - Background task scheduling

### New API Endpoints
- `GET /api/settings` - Get user settings
- `POST /api/settings` - Update user settings
- `POST /api/trigger-check` - Manually trigger release check

### Background Tasks
- **Startup Checks**: Triggered 30 seconds after app start
- **Periodic Checks**: Run at user-configured intervals
- **Token Refresh**: Automatic Spotify token refresh
- **Download Queue**: Sequential processing of new releases

## 🛡️ Security & Privacy

### Data Storage
- **Local Only**: All data stored locally on your machine
- **No Cloud Sync**: Sessions and settings never leave your device
- **Encrypted Tokens**: Spotify tokens are stored securely
- **User Isolation**: Each user's data is completely separate

### Privacy
- **No Tracking**: No usage analytics or tracking
- **Spotify Only**: Only communicates with Spotify's official API
- **Minimal Data**: Only stores necessary authentication and preference data

## 🔍 Monitoring & Logs

### Activity Logs
- **Release Detection**: Logs when new releases are found
- **Download Activity**: Tracks all automatic downloads
- **Error Handling**: Logs any issues for troubleshooting
- **User Actions**: Records manual checks and setting changes

### Status Indicators
- **Real-Time Feedback**: Immediate status updates in the UI
- **Background Activity**: Visual indicators when checks are running
- **Error Notifications**: Clear error messages when issues occur

## 🚨 Troubleshooting

### Common Issues

#### Session Not Persisting
- Check if `sessions/` directory exists and is writable
- Verify Spotify credentials in `.env` file
- Try logging out and logging back in

#### Automatic Checks Not Working
- Ensure "Enable automatic checking" is turned on
- Check that the app is running (background scheduler needs the app active)
- Verify internet connection for Spotify API access

#### Downloads Not Starting
- Confirm `spotdl` is installed and working
- Check that auto-download is enabled in settings
- Verify sufficient disk space in the `music/` directory

### Debug Mode
Run the app with debug output:
```bash
python app.py
```
Check console output for detailed logging information.

## 🎯 Future Enhancements

### Planned Features
- **Desktop Notifications**: System notifications for new releases
- **Email Alerts**: Optional email notifications
- **Advanced Filtering**: Filter releases by date, type, or other criteria
- **Batch Operations**: Bulk enable/disable artists for checking
- **Statistics Dashboard**: View checking and download statistics

### Performance Improvements
- **Incremental Checks**: Only check for changes since last check
- **Parallel Downloads**: Multiple concurrent downloads (configurable)
- **Smart Scheduling**: Adaptive check intervals based on activity
- **Caching**: Cache artist data to reduce API calls

---

## 📞 Support

If you encounter any issues with the new features:

1. **Check Logs**: Look at console output and log files
2. **Test Components**: Run `python test_session_manager.py`
3. **Reset Settings**: Delete `user_settings/` directory to reset
4. **Fresh Start**: Delete `sessions/` directory to clear all sessions

The original functionality remains unchanged - you can still use the app exactly as before, with these new features as optional enhancements!
