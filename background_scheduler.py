"""
Background Scheduler for Artist Releases Downloader
Handles periodic tasks like checking for new releases and automatic downloads
"""

import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import Cron<PERSON>rigger
import spotipy
from spotipy.oauth2 import SpotifyOAuth

from session_manager import SessionManager


class BackgroundTaskScheduler:
    """Manages background tasks for automatic checking and downloading"""
    
    def __init__(self, session_manager: SessionManager, spotify_oauth: SpotifyOAuth):
        self.session_manager = session_manager
        self.spotify_oauth = spotify_oauth
        self.scheduler = BackgroundScheduler()
        self.is_running = False
        
        # Create logs directory
        os.makedirs('logs', exist_ok=True)
        
    def start(self):
        """Start the background scheduler"""
        if not self.is_running:
            self.scheduler.start()
            self.is_running = True
            print("🚀 Background scheduler started")
            
            # Schedule startup check
            self.schedule_startup_checks()
    
    def stop(self):
        """Stop the background scheduler"""
        if self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            print("🛑 Background scheduler stopped")
    
    def schedule_startup_checks(self):
        """Schedule checks for users who have startup checking enabled"""
        try:
            active_users = self.session_manager.get_all_active_users()
            
            for user_info in active_users:
                user_id = user_info['user_id']
                settings = self.session_manager.load_user_settings(user_id)
                
                if settings.get('check_on_startup', True):
                    # Schedule a one-time check in 30 seconds (to allow app to fully start)
                    self.scheduler.add_job(
                        func=self.check_for_new_releases,
                        trigger='date',
                        run_date=datetime.now() + timedelta(seconds=30),
                        args=[user_id],
                        id=f'startup_check_{user_id}',
                        replace_existing=True
                    )
                    print(f"📅 Scheduled startup check for user {user_info['user_name']}")
                
        except Exception as e:
            print(f"❌ Error scheduling startup checks: {e}")
    
    def schedule_periodic_checks(self, user_id: str):
        """Schedule periodic checks for a user based on their settings"""
        try:
            settings = self.session_manager.load_user_settings(user_id)
            
            if not settings.get('auto_check_enabled', False):
                # Remove existing job if auto-check is disabled
                self.remove_user_periodic_job(user_id)
                return
            
            interval_hours = settings.get('auto_check_interval_hours', 6)
            
            # Remove existing job first
            self.remove_user_periodic_job(user_id)
            
            # Add new periodic job
            self.scheduler.add_job(
                func=self.check_for_new_releases,
                trigger=IntervalTrigger(hours=interval_hours),
                args=[user_id],
                id=f'periodic_check_{user_id}',
                replace_existing=True
            )
            
            user_info = self.session_manager.load_session(user_id)
            user_name = user_info.get('user_name', user_id) if user_info else user_id
            
            print(f"📅 Scheduled periodic checks every {interval_hours} hours for user {user_name}")
            
        except Exception as e:
            print(f"❌ Error scheduling periodic checks for {user_id}: {e}")
    
    def remove_user_periodic_job(self, user_id: str):
        """Remove periodic job for a user"""
        try:
            job_id = f'periodic_check_{user_id}'
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                print(f"🗑️ Removed periodic job for user {user_id}")
        except Exception as e:
            print(f"❌ Error removing periodic job for {user_id}: {e}")
    
    def check_for_new_releases(self, user_id: str):
        """Check for new releases for a specific user"""
        try:
            print(f"🔍 Checking for new releases for user {user_id}")
            
            # Get user session and settings
            session_data = self.session_manager.load_session(user_id)
            if not session_data:
                print(f"❌ No valid session for user {user_id}")
                return
            
            settings = self.session_manager.load_user_settings(user_id)
            user_name = session_data.get('user_name', user_id)
            
            # Get Spotify client
            sp = self.session_manager.get_valid_spotify_client(user_id, self.spotify_oauth)
            if not sp:
                print(f"❌ Could not get Spotify client for user {user_name}")
                return
            
            new_releases_found = False
            
            # Check artists if enabled
            if settings.get('download_artists', True):
                artist_releases = self.check_artist_releases(sp, user_id)
                if artist_releases:
                    new_releases_found = True
                    self.log_new_releases(user_id, 'artists', artist_releases)
                    
                    if settings.get('auto_download_enabled', False):
                        self.trigger_download(user_id, 'artists')
            
            # Check liked songs if enabled
            if settings.get('download_liked', True):
                liked_changes = self.check_liked_songs(sp, user_id)
                if liked_changes:
                    new_releases_found = True
                    self.log_new_releases(user_id, 'liked', liked_changes)
                    
                    if settings.get('auto_download_enabled', False):
                        self.trigger_download(user_id, 'liked')
            
            if new_releases_found:
                print(f"🎉 Found new releases for user {user_name}")
            else:
                print(f"📭 No new releases found for user {user_name}")
                
        except Exception as e:
            print(f"❌ Error checking releases for user {user_id}: {e}")
    
    def check_artist_releases(self, sp: spotipy.Spotify, user_id: str) -> List[Dict]:
        """Check for new artist releases"""
        try:
            # Load current artist data
            if not os.path.exists('artist_songs.json'):
                return []
            
            with open('artist_songs.json', 'r') as f:
                current_data = json.load(f)
            
            new_releases = []
            
            for artist_name, artist_data in current_data.items():
                try:
                    # Get fresh data from Spotify
                    # This is a simplified check - in a full implementation,
                    # you'd want to store artist IDs and check for new albums/singles
                    
                    # For now, we'll just log that we're checking
                    print(f"🎵 Checking {artist_name} for new releases...")
                    
                    # TODO: Implement actual new release detection logic
                    # This would involve comparing current data with fresh Spotify data
                    
                except Exception as e:
                    print(f"❌ Error checking artist {artist_name}: {e}")
                    continue
            
            return new_releases
            
        except Exception as e:
            print(f"❌ Error checking artist releases: {e}")
            return []
    
    def check_liked_songs(self, sp: spotipy.Spotify, user_id: str) -> List[Dict]:
        """Check for changes in liked songs"""
        try:
            # Get current liked songs from Spotify
            results = sp.current_user_saved_tracks(limit=50)
            if not results:
                return []
            
            current_liked = []
            while results:
                for item in results.get("items", []):
                    track = item.get("track")
                    if track:
                        current_liked.append({
                            "id": track.get("id"),
                            "name": track.get("name", ""),
                            "artist": track.get("artists", [{}])[0].get("name", ""),
                            "added_at": item.get("added_at", "")
                        })
                
                if results.get("next"):
                    results = sp.next(results)
                else:
                    break
            
            # Compare with stored liked songs
            liked_file = 'liked_songs.json'
            if os.path.exists(liked_file):
                with open(liked_file, 'r') as f:
                    stored_liked = json.load(f)
                
                # Find new songs (simplified comparison by track ID)
                stored_ids = {song.get('id') for song in stored_liked if song.get('id')}
                current_ids = {song['id'] for song in current_liked if song.get('id')}
                
                new_song_ids = current_ids - stored_ids
                new_songs = [song for song in current_liked if song['id'] in new_song_ids]
                
                if new_songs:
                    # Update stored liked songs
                    with open(liked_file, 'w') as f:
                        json.dump(current_liked, f, indent=2)
                    
                    return new_songs
            else:
                # First time checking, save current state
                with open(liked_file, 'w') as f:
                    json.dump(current_liked, f, indent=2)
                
                return current_liked  # All songs are "new" on first check
            
            return []
            
        except Exception as e:
            print(f"❌ Error checking liked songs: {e}")
            return []
    
    def log_new_releases(self, user_id: str, release_type: str, releases: List[Dict]):
        """Log new releases to file"""
        try:
            log_file = f'logs/{user_id}_new_releases.json'
            
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'user_id': user_id,
                'type': release_type,
                'count': len(releases),
                'releases': releases
            }
            
            # Load existing logs
            logs = []
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    logs = json.load(f)
            
            logs.append(log_entry)
            
            # Keep only last 100 entries
            logs = logs[-100:]
            
            with open(log_file, 'w') as f:
                json.dump(logs, f, indent=2)
            
            print(f"📝 Logged {len(releases)} new {release_type} releases for user {user_id}")
            
        except Exception as e:
            print(f"❌ Error logging new releases: {e}")
    
    def trigger_download(self, user_id: str, download_type: str):
        """Trigger automatic download for a user"""
        try:
            print(f"⬇️ Triggering automatic download of {download_type} for user {user_id}")
            
            # This would integrate with the existing download system
            # For now, we'll just log the action
            
            # TODO: Integrate with the existing download functions from app.py
            # This would require refactoring the download functions to work without WebSocket
            
        except Exception as e:
            print(f"❌ Error triggering download: {e}")
    
    def get_scheduled_jobs(self) -> List[Dict]:
        """Get list of currently scheduled jobs"""
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger)
                })
            return jobs
        except Exception as e:
            print(f"❌ Error getting scheduled jobs: {e}")
            return []
