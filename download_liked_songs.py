import json
import os
import subprocess
import re
from datetime import datetime

def normalize_filename(name):
    # Remove or replace invalid characters for filenames
    name = re.sub(r'[<>:"/\\|?*]', '', name)
    return name

def safe_print(message):
    """Print message with Unicode fallback for Windows console"""
    try:
        print(message)
    except UnicodeEncodeError:
        # Remove emojis and special Unicode characters
        safe_message = re.sub(r'[^\x00-\x7F]+', '', message)
        print(safe_message)

def main():
    safe_print("💖 Let's download your liked songs! Hold onto your heart! 💕")
    # Load liked_songs.json
    safe_print("📖 Loading liked_songs.json... Getting your favorites ready! 📚")
    with open('liked_songs.json', 'r') as f:
        liked_songs_data = json.load(f)
    safe_print(f"✨ Loaded {len(liked_songs_data)} liked songs! Time to groove! 🕺")

    # Load or create downloaded_liked.json
    downloaded_json_path = 'downloaded_liked.json'
    if os.path.exists(downloaded_json_path):
        safe_print("📋 Loading downloaded_liked.json... Checking our download history! 📜")
        with open(downloaded_json_path, 'r') as f:
            downloaded_songs = json.load(f)
        safe_print(f"📂 Found {len(downloaded_songs)} previously downloaded liked songs! 🎵")
    else:
        downloaded_songs = []
        safe_print("📋 No downloaded_liked.json yet. Starting fresh! 🆕")

    # Also load downloaded.json to avoid duplicates
    all_downloaded_songs = list(downloaded_songs)
    if os.path.exists('downloaded.json'):
        safe_print("📋 Also checking downloaded.json to avoid duplicates... 🔍")
        with open('downloaded.json', 'r') as f:
            artist_downloaded = json.load(f)
        # Add artist songs to our check list
        for artist_song in artist_downloaded:
            # Check if this song is already in our liked list
            already_exists = any(
                ds.get('artist') == artist_song.get('artist') and
                ds.get('song_name') == artist_song.get('song_name')
                for ds in all_downloaded_songs
            )
            if not already_exists:
                all_downloaded_songs.append(artist_song)
        safe_print(f"📊 Total songs to check against: {len(all_downloaded_songs)} (including artist songs)")
    else:
        safe_print("📝 No downloaded.json found, only checking liked downloads")
        all_downloaded_songs = downloaded_songs

    # Collect all songs from liked_songs.json
    print("🎸 Collecting all your liked songs... Let's make a mega playlist! 📀")
    all_songs = []
    for song in liked_songs_data:
        all_songs.append({
            'artist': song.get('artist', 'Unknown Artist'),
            'song_name': song.get('name', ''),
            'link': song.get('link', '')
        })
    print(f"🎶 Collected {len(all_songs)} liked songs in total! Wow, that's a lot of tunes! 🎼")

    # List downloaded files, checking subfolders per artist
    music_dir = 'music'
    if not os.path.exists(music_dir):
        os.makedirs(music_dir)
        print("📁 Created music directory! Fresh start! 🆕")

    print("🔍 Checking what songs are already downloaded... Let's see what's missing! 👀")
    downloaded_files = {}
    for artist in set(song['artist'] for song in all_songs):
        # Use normalized artist name for directory lookup
        normalized_artist = normalize_filename(artist)
        artist_dir = os.path.join(music_dir, normalized_artist)
        if os.path.exists(artist_dir):
            downloaded_files[artist] = [normalize_filename(f).lower() for f in os.listdir(artist_dir)]
            print(f"📂 Found {len(downloaded_files[artist])} files for {artist}! 🎵")
        else:
            downloaded_files[artist] = []
            print(f"📂 No folder yet for {artist}. We'll create one! 🛠️")

    # Find missing songs
    print("🕵️‍♀️ Hunting for missing liked songs... Which ones are we missing? 🔍")
    missing_songs = []
    for song in all_songs:
        artist = song['artist']
        expected_filename = f"{song['artist']} - {song['song_name']}.mp3"
        normalized_expected = normalize_filename(expected_filename).lower()
        # Check if in folder
        in_folder = normalized_expected in downloaded_files.get(artist, [])
        # Check if in ANY downloaded.json (liked or artists)
        in_downloaded = any(
            ds.get('artist') == song['artist'] and ds.get('song_name') == song['song_name']
            for ds in all_downloaded_songs
        )
        if not in_folder and not in_downloaded:
            missing_songs.append(song)
        elif in_downloaded and not in_folder:
            safe_print(f"⏭️ Skipping {song['artist']} - {song['song_name']}: Already downloaded before! 📋")

    print(f"📝 Found {len(missing_songs)} missing liked songs! Let's queue them up! 📋")
    # Save queue_liked_songs.json
    print("💾 Saving the missing liked songs to queue_liked_songs.json... Get ready to download! 🌟")
    with open('queue_liked_songs.json', 'w') as f:
        json.dump(missing_songs, f, indent=4)
    print("✅ Queue saved! Time to start downloading! 🚀")

    # Download missing songs sequentially
    print("⬇️ Starting the download adventure! One song at a time... 🎧")
    for i, song in enumerate(missing_songs):
        # Normalize artist name for directory creation
        normalized_artist = normalize_filename(song['artist'])
        artist_dir = os.path.join(music_dir, normalized_artist)
        if not os.path.exists(artist_dir):
            os.makedirs(artist_dir)
            print(f"📁 Created folder for {song['artist']}! 🆕")
        print(f"🎵 Downloading {i+1}/{len(missing_songs)}: {song['artist']} - {song['song_name']}... Hold tight! 🎶")
        try:
            result = subprocess.run(['spotdl', song['link'], '--output', artist_dir], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Downloaded successfully! 🎉")
                # Add to downloaded_songs (use original artist name for tracking)
                downloaded_songs.append({
                    'artist': song['artist'],
                    'song_name': song['song_name'],
                    'link': song['link'],
                    'downloaded_at': datetime.now().isoformat()
                })
                # Save downloaded.json
                with open(downloaded_json_path, 'w') as f:
                    json.dump(downloaded_songs, f, indent=4)
                print("📋 Added to downloaded.json! 📝")
            else:
                print(f"❌ Failed to download: {result.stderr} 😞")
        except Exception as e:
            print(f"😱 Error downloading {song['link']}: {e} 🐛")
    print("🎊 All downloads complete! Your liked songs library is now epic! 🚀")

if __name__ == "__main__":
    main()