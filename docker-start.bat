@echo off
REM Artist Releases Downloader - Docker Startup Script for Windows
REM This script helps you easily start the application with Docker

echo 🎵 Artist Releases Downloader - Docker Setup
echo =============================================

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    echo    Visit: https://docs.docker.com/desktop/windows/
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose is not installed. Please install Docker Desktop first.
    echo    Visit: https://docs.docker.com/desktop/windows/
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo 📝 Creating .env file from template...
    copy .env.example .env >nul
    echo ✅ Created .env file
    echo.
    echo 🔧 IMPORTANT: Please edit .env file with your settings:
    echo    1. Set your Spotify API credentials (SPOTIPY_CLIENT_ID, SPOTIPY_CLIENT_SECRET^)
    echo    2. Set your music download path (MUSIC_PATH^)
    echo    3. Optionally set your timezone (TZ^)
    echo.
    echo 📖 For detailed setup instructions, see DOCKER_SETUP.md
    echo.
    pause
)

REM Display current configuration
echo 📋 Current Configuration:
echo    Access URL: http://localhost:5000
echo    Music Path: Check your .env file for MUSIC_PATH setting
echo.

REM Start the application
echo 🚀 Starting Artist Releases Downloader...
echo    Building container (this may take a few minutes on first run^)...

REM Build and start
docker-compose up -d --build

REM Wait a moment for startup
timeout /t 5 /nobreak >nul

REM Check if container is running
docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    echo.
    echo ❌ Failed to start the application
    echo 📋 Check the logs for errors:
    echo    docker-compose logs
    pause
    exit /b 1
) else (
    echo.
    echo ✅ SUCCESS! Artist Releases Downloader is running!
    echo.
    echo 🌐 Access the application at: http://localhost:5000
    echo 📁 Music will be downloaded to your configured MUSIC_PATH
    echo.
    echo 📊 Useful commands:
    echo    View logs:    docker-compose logs -f
    echo    Stop app:     docker-compose down
    echo    Restart app:  docker-compose restart
    echo.
    echo 📖 For more information, see DOCKER_SETUP.md
    echo.
    echo Opening browser...
    start http://localhost:5000
)

pause
