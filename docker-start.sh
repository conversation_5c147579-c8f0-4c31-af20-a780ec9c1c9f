#!/bin/bash

# Artist Releases Downloader - Docker Startup Script
# This script helps you easily start the application with Docker

set -e

echo "🎵 Artist Releases Downloader - Docker Setup"
echo "============================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "   Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ Created .env file"
    echo ""
    echo "🔧 IMPORTANT: Please edit .env file with your settings:"
    echo "   1. Set your Spotify API credentials (SPOTIPY_CLIENT_ID, SPOTIPY_CLIENT_SECRET)"
    echo "   2. Set your music download path (MUSIC_PATH)"
    echo "   3. Optionally set your timezone (TZ)"
    echo ""
    echo "📖 For detailed setup instructions, see DOCKER_SETUP.md"
    echo ""
    read -p "Press Enter after you've configured .env file..."
fi

# Load environment variables
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Check if required environment variables are set
if [ -z "$SPOTIPY_CLIENT_ID" ] || [ -z "$SPOTIPY_CLIENT_SECRET" ]; then
    echo "⚠️  WARNING: Spotify API credentials not set in .env file"
    echo "   Please edit .env and set SPOTIPY_CLIENT_ID and SPOTIPY_CLIENT_SECRET"
    echo ""
fi

# Display current configuration
echo "📋 Current Configuration:"
echo "   Music Path: ${MUSIC_PATH:-./music}"
echo "   Timezone: ${TZ:-UTC}"
echo "   Redirect URI: ${SPOTIPY_REDIRECT_URI:-http://localhost:5000/callback}"
echo ""

# Create music directory if it doesn't exist
if [ ! -d "${MUSIC_PATH:-./music}" ]; then
    echo "📁 Creating music directory: ${MUSIC_PATH:-./music}"
    mkdir -p "${MUSIC_PATH:-./music}"
fi

# Start the application
echo "🚀 Starting Artist Releases Downloader..."
echo "   Building container (this may take a few minutes on first run)..."

# Build and start
docker-compose up -d --build

# Wait a moment for startup
sleep 5

# Check if container is running
if docker-compose ps | grep -q "Up"; then
    echo ""
    echo "✅ SUCCESS! Artist Releases Downloader is running!"
    echo ""
    echo "🌐 Access the application at: http://localhost:5000"
    echo "📁 Music will be downloaded to: ${MUSIC_PATH:-./music}"
    echo ""
    echo "📊 Useful commands:"
    echo "   View logs:    docker-compose logs -f"
    echo "   Stop app:     docker-compose down"
    echo "   Restart app:  docker-compose restart"
    echo ""
    echo "📖 For more information, see DOCKER_SETUP.md"
else
    echo ""
    echo "❌ Failed to start the application"
    echo "📋 Check the logs for errors:"
    echo "   docker-compose logs"
    exit 1
fi
