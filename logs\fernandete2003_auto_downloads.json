[{"timestamp": "2025-09-22T18:45:37.217174", "user_id": "fernandete2003", "download_type": "artists", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_artists_songs.py\", line 115, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_artists_songs.py\", line 13, in main\n    print(\"\\U0001f3b5 Let's rock and roll! Starting the download party! \\U0001f3b6\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f3b5' in position 0: character maps to <undefined>\n"}, {"timestamp": "2025-09-22T18:45:39.489019", "user_id": "fernandete2003", "download_type": "artists", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_artists_songs.py\", line 115, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_artists_songs.py\", line 13, in main\n    print(\"\\U0001f3b5 Let's rock and roll! Starting the download party! \\U0001f3b6\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f3b5' in position 0: character maps to <undefined>\n"}, {"timestamp": "2025-09-22T18:45:40.717774", "user_id": "fernandete2003", "download_type": "liked", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 116, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 13, in main\n    print(\"\\U0001f496 Let's download your liked songs! Hold onto your heart! \\U0001f495\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f496' in position 0: character maps to <undefined>\n"}, {"timestamp": "2025-09-22T18:45:43.192345", "user_id": "fernandete2003", "download_type": "liked", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 116, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 13, in main\n    print(\"\\U0001f496 Let's download your liked songs! Hold onto your heart! \\U0001f495\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f496' in position 0: character maps to <undefined>\n"}, {"timestamp": "2025-09-22T18:48:12.916169", "user_id": "fernandete2003", "download_type": "liked", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 116, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 13, in main\n    print(\"\\U0001f496 Let's download your liked songs! Hold onto your heart! \\U0001f495\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f496' in position 0: character maps to <undefined>\n"}, {"timestamp": "2025-09-22T18:48:14.810274", "user_id": "fernandete2003", "download_type": "liked", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 116, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 13, in main\n    print(\"\\U0001f496 Let's download your liked songs! Hold onto your heart! \\U0001f495\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f496' in position 0: character maps to <undefined>\n"}, {"timestamp": "2025-09-22T18:48:42.049849", "user_id": "fernandete2003", "download_type": "artists", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_artists_songs.py\", line 115, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_artists_songs.py\", line 13, in main\n    print(\"\\U0001f3b5 Let's rock and roll! Starting the download party! \\U0001f3b6\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f3b5' in position 0: character maps to <undefined>\n"}, {"timestamp": "2025-09-22T18:48:45.179696", "user_id": "fernandete2003", "download_type": "artists", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_artists_songs.py\", line 115, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_artists_songs.py\", line 13, in main\n    print(\"\\U0001f3b5 Let's rock and roll! Starting the download party! \\U0001f3b6\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f3b5' in position 0: character maps to <undefined>\n"}, {"timestamp": "2025-09-22T18:48:45.272943", "user_id": "fernandete2003", "download_type": "liked", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 116, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 13, in main\n    print(\"\\U0001f496 Let's download your liked songs! Hold onto your heart! \\U0001f495\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f496' in position 0: character maps to <undefined>\n"}, {"timestamp": "2025-09-22T18:48:48.182418", "user_id": "fernandete2003", "download_type": "artists", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_artists_songs.py\", line 115, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_artists_songs.py\", line 13, in main\n    print(\"\\U0001f3b5 Let's rock and roll! Starting the download party! \\U0001f3b6\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f3b5' in position 0: character maps to <undefined>\n"}, {"timestamp": "2025-09-22T18:48:48.619612", "user_id": "fernandete2003", "download_type": "liked", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 116, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 13, in main\n    print(\"\\U0001f496 Let's download your liked songs! Hold onto your heart! \\U0001f495\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f496' in position 0: character maps to <undefined>\n"}, {"timestamp": "2025-09-22T18:48:50.868449", "user_id": "fernandete2003", "download_type": "liked", "success": false, "details": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 116, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\Documents\\Projects\\artist-releases-downloader\\download_liked_songs.py\", line 13, in main\n    print(\"\\U0001f496 Let's download your liked songs! Hold onto your heart! \\U0001f495\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f496' in position 0: character maps to <undefined>\n"}]